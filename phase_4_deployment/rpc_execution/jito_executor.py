#!/usr/bin/env python3
"""
Jito Executor Module

This module is responsible for executing trades using Jito RPC
for MEV protection and optimal execution.
"""

import os
import json
import logging
import asyncio
import base64
import time
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from solders.commitment_config import CommitmentLevel
from solders.rpc.config import RpcSendTransactionConfig
from solders.transaction import Transaction
from solders.pubkey import Pubkey as PublicKey
from solders.keypair import Keypair

# Import custom Jito client
from rpc_execution.jito_client import JitoClient

# Import secure wallet
from wallet_sync.secure_wallet import SecureWallet

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'output', 'execution_log.txt'
        )),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('jito_executor')

class JitoExecutor:
    """
    Executor for sending trades using Jito RPC for MEV protection
    and optimal execution.
    """

    def __init__(self,
                 config_path: str = None,
                 jito_rpc_url: str = None,
                 fallback_rpc_url: str = None,
                 wallet_address: str = None,
                 auth_keypair_path: str = None,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 dry_run: bool = False):
        """
        Initialize the JitoExecutor.

        Args:
            config_path: Path to the Jito configuration file
            jito_rpc_url: Jito RPC URL (overrides config)
            fallback_rpc_url: Fallback RPC URL if Jito fails (overrides config)
            wallet_address: Public key of the wallet to use (overrides environment)
            auth_keypair_path: Path to the Ed25519 keypair for authentication (overrides config)
            max_retries: Maximum number of retry attempts (overrides config)
            retry_delay: Delay between retry attempts in seconds (overrides config)
            dry_run: Whether to run in dry-run mode (no actual transactions)
        """
        # Load configuration
        self.config = self._load_config(config_path)

        # Override config with provided parameters
        self.jito_rpc_url = jito_rpc_url or self.config['rpc']['jito_url']
        self.fallback_rpc_url = fallback_rpc_url or self.config['rpc']['fallback_url']
        self.wallet_address = wallet_address or os.environ.get('WALLET_ADDRESS')
        self.auth_keypair_path = auth_keypair_path or self.config['auth']['keypair_path']
        self.max_retries = max_retries or self.config['transaction']['max_retries']
        self.retry_delay = retry_delay or self.config['transaction']['retry_delay']
        self.dry_run = dry_run

        # Initialize Jito client
        self.jito_client = JitoClient(
            rpc_url=self.jito_rpc_url,
            fallback_rpc_url=self.fallback_rpc_url,
            auth_keypair_path=self.auth_keypair_path if self.config['auth']['use_auth_for_tx'] else None,
            max_retries=self.max_retries,
            retry_delay=self.retry_delay
        )

        # Initialize secure wallet
        self.secure_wallet = SecureWallet()
        self.keypair = None

        # Load wallet if not in dry run mode
        if not self.dry_run and self.wallet_address:
            try:
                # Load wallet from secure storage
                public_key, private_key = self.secure_wallet.load_wallet(self.wallet_address)
                self.keypair = Keypair.from_base58_string(private_key)
                logger.info(f"Loaded wallet: {public_key}")
            except Exception as e:
                logger.error(f"Failed to load wallet: {str(e)}")
                logger.warning("Running without a wallet - transactions will not be signed")

        # Transaction history
        self.tx_history = []

        logger.info(f"Initialized JitoExecutor with Jito RPC: {self.jito_rpc_url}")
        if self.dry_run:
            logger.info("Running in DRY RUN mode - no actual transactions will be executed")

    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """
        Load the Jito configuration from YAML.

        Args:
            config_path: Path to the configuration file

        Returns:
            Dict containing configuration parameters
        """
        # Default config path
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'configs', 'jito_config.yaml'
            )

        # Default configuration
        default_config = {
            'rpc': {
                'jito_url': 'https://mainnet.block-engine.jito.wtf/api/v1/transactions',
                'fallback_url': 'https://api.mainnet-beta.solana.com',
                'shredstream_url': 'wss://shredstream.jito.wtf/stream'
            },
            'auth': {
                'keypair_path': 'keys/jito_shredstream_keypair.json',
                'use_auth_for_tx': False
            },
            'transaction': {
                'max_retries': 3,
                'retry_delay': 1.0,
                'timeout': 30.0,
                'default_tip': 10000,
                'skip_preflight': False,
                'max_confirmation_blocks': 32
            },
            'circuit_breaker': {
                'failure_threshold': 5,
                'reset_timeout': 60.0,
                'enabled': True
            },
            'monitoring': {
                'enabled': True,
                'metrics_path': 'output/jito_metrics.json',
                'update_interval': 60.0,
                'verbose_logging': False
            },
            'shredstream': {
                'enabled': False,
                'max_reconnects': 10,
                'reconnect_delay': 5.0,
                'buffer_size': 1000
            }
        }

        try:
            # Load configuration from file if it exists
            if os.path.exists(config_path):
                with open(config_path, 'r') as file:
                    config = yaml.safe_load(file)
                logger.info(f"Loaded configuration from {config_path}")

                # Merge with default config to ensure all keys exist
                merged_config = default_config.copy()
                for section, values in config.items():
                    if section in merged_config and isinstance(values, dict):
                        merged_config[section].update(values)
                    else:
                        merged_config[section] = values

                return merged_config
            else:
                logger.warning(f"Configuration file {config_path} not found, using default configuration")
                return default_config
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            return default_config

    def _sign_transaction(self, transaction: Transaction) -> bool:
        """
        Sign a transaction with the wallet keypair.

        Args:
            transaction: Transaction to sign

        Returns:
            True if signed successfully, False otherwise
        """
        if self.keypair is None:
            logger.warning("No keypair available for signing")
            return False

        try:
            # FIXED: Get fresh blockhash immediately before signing to prevent signature verification failure
            fresh_blockhash = self.rpc_client.get_latest_blockhash()
            if fresh_blockhash and hasattr(fresh_blockhash, 'value'):
                transaction.recent_blockhash = fresh_blockhash.value.blockhash
                logger.debug(f"Updated transaction with fresh blockhash: {transaction.recent_blockhash}")

            # Sign with fresh blockhash
            transaction.sign_partial([self.keypair])

            # Verify the signature was applied
            if not transaction.signatures or not transaction.signatures[0]:
                logger.error("Transaction signing failed - no signature generated")
                return False

            logger.debug(f"Transaction signed successfully with signature: {transaction.signatures[0]}")
            return True
        except Exception as e:
            logger.error(f"Failed to sign transaction: {str(e)}")
            return False

    async def get_bundle_fee(self) -> int:
        """
        Get the current bundle fee for Jito bundles.

        Returns:
            Bundle fee in lamports
        """
        try:
            response = await self.jito_client.get_recent_prioritization_fees()
            if response.get('result'):
                fees = response['result']
                # Calculate a reasonable bundle fee based on recent fees
                if fees:
                    max_fee = max(fee.get('prioritizationFee', 0) for fee in fees)
                    return max_fee * 2  # Double the max fee for priority

            # Default fee if unable to get recent fees
            return 10000  # 10,000 lamports
        except Exception as e:
            logger.error(f"Failed to get bundle fee: {str(e)}")
            return 10000  # Default fee

    async def send_transaction(self,
                              transaction: Transaction,
                              opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Send a transaction using Jito RPC.

        Args:
            transaction: Transaction to send
            opts: Transaction options

        Returns:
            Dict containing transaction result with keys:
                - success: Whether the transaction was sent successfully
                - signature: Transaction signature if successful
                - provider: Provider used ('jito' or 'fallback')
                - error: Error message if unsuccessful
        """
        # Default options
        if opts is None:
            opts = {
                'skip_preflight': self.config['transaction']['skip_preflight'],
                'max_retries': 0,  # We handle retries ourselves
                'tip': self.config['transaction']['default_tip']
            }

        # Add recent blockhash if not already set
        if transaction.recent_blockhash is None:
            try:
                # Use the Jito client to get a recent blockhash
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getRecentBlockhash",
                    "params": [{"commitment": "confirmed"}]
                }

                response = await self.jito_client.http_client.post(
                    self.jito_rpc_url,
                    json=payload
                )
                response.raise_for_status()
                result = response.json()

                if 'result' in result and 'value' in result['result'] and 'blockhash' in result['result']['value']:
                    transaction.recent_blockhash = result['result']['value']['blockhash']
                else:
                    logger.error(f"Failed to get recent blockhash: {result.get('error')}")
                    return {
                        'success': False,
                        'error': f"Failed to get recent blockhash: {result.get('error')}",
                        'provider': None
                    }
            except Exception as e:
                logger.error(f"Failed to get recent blockhash: {str(e)}")
                return {
                    'success': False,
                    'error': f"Failed to get recent blockhash: {str(e)}",
                    'provider': None
                }

        # Sign transaction with wallet keypair
        self._sign_transaction(transaction)

        # Serialize transaction with proper encoding validation
        try:
            # FIXED: Improved transaction serialization for Jito compatibility
            serialized_tx = transaction.serialize()

            # Validate serialization
            if not serialized_tx or len(serialized_tx) == 0:
                raise ValueError("Transaction serialization resulted in empty bytes")

            # Validate base64 encoding compatibility
            try:
                test_encoded = base64.b64encode(serialized_tx).decode('utf-8')
                base64.b64decode(test_encoded, validate=True)
                logger.debug(f"Transaction serialized successfully: {len(serialized_tx)} bytes")
            except Exception as encoding_error:
                raise ValueError(f"Transaction serialization failed base64 validation: {encoding_error}")

        except Exception as e:
            logger.error(f"Failed to serialize transaction: {str(e)}")
            return {
                'success': False,
                'error': f"Failed to serialize transaction: {str(e)}",
                'provider': None
            }

        # Execute real transaction only - no simulation modes

        # Send transaction using Jito client
        result = await self.jito_client.send_transaction(serialized_tx, opts)

        # Record in history if successful
        if result['success']:
            self.tx_history.append({
                'timestamp': time.time(),
                'signature': result['signature'],
                'status': 'sent' if result['provider'] == 'jito' else 'sent_fallback',
                'provider': result['provider']
            })

        return result

    async def send_bundle(self,
                         transactions: List[Transaction],
                         opts: Dict[str, Any] = None) -> Optional[str]:
        """
        Send a bundle of transactions using Jito RPC.

        Args:
            transactions: List of transactions to send as a bundle
            opts: Transaction options

        Returns:
            Bundle UUID if successful, None otherwise
        """
        if opts is None:
            opts = {
                "skip_preflight": False,
                "preflight_commitment": "confirmed"
            }

        # Get bundle fee
        bundle_fee = await self.get_bundle_fee()

        # Prepare transactions
        serialized_txs = []
        for i, tx in enumerate(transactions):
            # Add recent blockhash if not already set
            if tx.recent_blockhash is None:
                try:
                    recent_blockhash = await self.jito_client.get_recent_blockhash()
                    tx.recent_blockhash = recent_blockhash['result']['value']['blockhash']
                except Exception as e:
                    logger.error(f"Failed to get recent blockhash: {str(e)}")
                    return None

            # Sign transaction with wallet keypair
            self._sign_transaction(tx)

            # Serialize transaction with proper validation
            try:
                # FIXED: Improved bundle transaction serialization
                tx_bytes = tx.serialize()

                # Validate serialization
                if not tx_bytes or len(tx_bytes) == 0:
                    logger.error(f"Transaction {i} serialization resulted in empty bytes")
                    return None

                # Encode to base64 with validation
                serialized_tx = base64.b64encode(tx_bytes).decode('utf-8')

                # Validate the encoding
                try:
                    base64.b64decode(serialized_tx, validate=True)
                except Exception as validation_error:
                    logger.error(f"Transaction {i} failed base64 validation: {validation_error}")
                    return None

                logger.debug(f"Transaction {i} serialized: {len(tx_bytes)} bytes -> {len(serialized_tx)} chars")

            except Exception as e:
                logger.error(f"Failed to serialize transaction {i} in bundle: {str(e)}")
                return None
            serialized_txs.append(serialized_tx)

        # Try to send bundle with retries
        for attempt in range(self.max_retries):
            try:
                # Note: This is a placeholder for the actual Jito bundle API
                # The actual implementation would use Jito's specific bundle API
                response = await self.jito_client.send_bundle(
                    serialized_txs,
                    bundle_fee=bundle_fee
                )

                if 'result' in response:
                    bundle_uuid = response['result']
                    logger.info(f"Bundle sent successfully: {bundle_uuid}")

                    # Record in history
                    self.tx_history.append({
                        'timestamp': time.time(),
                        'bundle_uuid': bundle_uuid,
                        'tx_count': len(transactions),
                        'status': 'bundle_sent'
                    })

                    return bundle_uuid
                else:
                    logger.warning(f"Failed to send bundle (attempt {attempt+1}/{self.max_retries}): {response.get('error')}")
            except Exception as e:
                logger.error(f"Error sending bundle (attempt {attempt+1}/{self.max_retries}): {str(e)}")

            # Wait before retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay)

        logger.error("Failed to send bundle after all attempts")
        return None

    async def get_transaction_status(self, signature: str) -> Dict[str, Any]:
        """
        Get the status of a transaction.

        Args:
            signature: Transaction signature

        Returns:
            Dict containing transaction status with keys:
                - success: Whether the status was retrieved successfully
                - status: Transaction status information if successful
                - provider: Provider used ('jito' or 'fallback')
                - error: Error message if unsuccessful
        """
        # Get real transaction status only - no simulation

        # Use Jito client to get transaction status
        return await self.jito_client.get_transaction_status(signature)

    def save_tx_history(self, file_path: str = None) -> None:
        """
        Save transaction history to file.

        Args:
            file_path: Path to save the transaction history file
        """
        if file_path is None:
            file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'output', 'tx_history.json'
            )

        try:
            # Load existing history if file exists
            existing_history = []
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        existing_history = data.get('transactions', [])
                except Exception as e:
                    logger.warning(f"Failed to load existing transaction history: {str(e)}")

            # Merge with new history
            merged_history = existing_history + self.tx_history

            # Remove duplicates based on signature
            unique_history = []
            seen_signatures = set()
            for tx in merged_history:
                signature = tx.get('signature')
                if signature and signature not in seen_signatures:
                    unique_history.append(tx)
                    seen_signatures.add(signature)

            # Sort by timestamp (descending)
            sorted_history = sorted(unique_history, key=lambda x: x.get('timestamp', 0), reverse=True)

            # Save to file
            with open(file_path, 'w') as f:
                json.dump({
                    'last_updated': time.time(),
                    'transactions': sorted_history
                }, f, indent=2)

            logger.info(f"Saved {len(sorted_history)} transactions to history")

            # Reset in-memory history after saving
            self.tx_history = []
        except Exception as e:
            logger.error(f"Failed to save transaction history: {str(e)}")

    def save_metrics(self, file_path: str = None) -> None:
        """
        Save client metrics to file.

        Args:
            file_path: Path to save the metrics file
        """
        if not self.config['monitoring']['enabled']:
            return

        if file_path is None:
            file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                self.config['monitoring']['metrics_path']
            )

        try:
            # Get metrics from Jito client
            client_metrics = self.jito_client.get_metrics()

            # Add executor metrics
            metrics = {
                'timestamp': time.time(),
                'client': client_metrics,
                'executor': {
                    'tx_history_count': len(self.tx_history),
                    'dry_run': self.dry_run
                }
            }

            # Save to file
            with open(file_path, 'w') as f:
                json.dump(metrics, f, indent=2)

            if self.config['monitoring']['verbose_logging']:
                logger.info(f"Saved metrics to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save metrics: {str(e)}")

    async def close(self):
        """Close the client sessions and save history."""
        # Save transaction history
        self.save_tx_history()

        # Save metrics
        self.save_metrics()

        # Close Jito client
        await self.jito_client.close()

        logger.info("JitoExecutor closed")

async def main():
    """Main function to demonstrate the executor."""
    # Check for dry run mode
    dry_run = os.environ.get('DRY_RUN', 'true').lower() == 'true'

    # First, ensure wallet is securely stored
    from wallet_sync.secure_wallet import SecureWallet
    import getpass

    # Get wallet address from environment
    wallet_address = os.environ.get('WALLET_ADDRESS')

    if not wallet_address:
        logger.warning("No wallet address found in environment. Running in demo mode.")
    else:
        # Check if wallet is already securely stored
        wallet = SecureWallet()
        wallet_file = os.path.join(wallet.wallet_dir, f"{wallet_address}.wallet")

        if not os.path.exists(wallet_file) and not dry_run:
            logger.info("Wallet not found in secure storage. Setting up secure wallet...")

            # Check if private key is in environment
            private_key = os.environ.get('WALLET_PRIVATE_KEY')
            if private_key:
                # Get password for encryption
                password = getpass.getpass("Enter password to encrypt wallet: ")
                password_confirm = getpass.getpass("Confirm password: ")

                if password != password_confirm:
                    logger.error("Passwords do not match. Exiting.")
                    return

                # Load or create encryption key
                wallet.load_encryption_key(password)

                # Create wallet
                public_key, _ = wallet.create_wallet(private_key)

                # Verify public key matches
                if public_key != wallet_address:
                    logger.error(f"Public key mismatch: {public_key} != {wallet_address}")
                    return

                # Save wallet
                wallet_file = wallet.save_wallet(public_key, private_key)
                logger.info(f"Wallet securely stored: {wallet_file}")
            else:
                logger.warning("No private key found in environment. Running without wallet.")

    # Create executor with config
    executor = JitoExecutor(
        config_path=os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'configs', 'jito_config.yaml'
        ),
        auth_keypair_path=os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'keys', 'jito_shredstream_keypair.json'
        ),
        wallet_address=wallet_address,
        dry_run=dry_run
    )

    logger.info(f"Jito Executor initialized with RPC URL: {executor.jito_rpc_url}")
    logger.info(f"Dry run mode: {dry_run}")

    # Example: Get bundle fee
    bundle_fee = await executor.get_bundle_fee()
    logger.info(f"Current bundle fee: {bundle_fee} lamports")

    # Example: Create a simple transaction (this is just a placeholder)
    from solders.transaction import Transaction
    from solders.system_program import transfer, TransferParams
    from solders.pubkey import Pubkey

    # Use the loaded wallet address if available, otherwise use a demo address
    if executor.keypair:
        sender = executor.keypair.pubkey()
        logger.info(f"Using loaded wallet for transaction: {sender}")
    else:
        sender = Pubkey.from_string("5ZWj7a1f8tWkjBESHKgrLmZhGYdFkK9fpN4e7R5Xmknp")  # Example address
        logger.info(f"Using demo wallet for transaction: {sender}")

    # Use the same address as receiver for demo
    receiver = sender

    # Create a transaction
    tx = Transaction()

    # Add a transfer instruction (self-transfer of minimal amount for demo)
    transfer_ix = transfer(
        TransferParams(
            from_pubkey=sender,
            to_pubkey=receiver,
            lamports=1000  # 0.000001 SOL
        )
    )
    tx.add(transfer_ix)

    # Send transaction
    logger.info("Sending example transaction...")
    result = await executor.send_transaction(tx)

    if result['success']:
        logger.info(f"Transaction sent successfully: {result['signature']}")

        # Get transaction status
        status_result = await executor.get_transaction_status(result['signature'])
        logger.info(f"Transaction status: {status_result}")
    else:
        logger.error(f"Failed to send transaction: {result.get('error')}")

    # Close executor
    await executor.close()

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run main function
    asyncio.run(main())
