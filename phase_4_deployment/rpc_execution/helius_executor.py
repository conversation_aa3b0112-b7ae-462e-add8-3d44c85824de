#!/usr/bin/env python3
"""
Helius Executor Module

This module is responsible for executing transactions using the Helius RPC.
"""

import os
import json
import yaml
import logging
import time
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union

# Import our PyO3-based package
try:
    from shared.solana_utils.tx_utils import (
        Transaction, VersionedTransaction, Keypair,
        sign_transaction
    )
    USING_RUST_UTILS = True
except ImportError:
    # Fallback to solders if solana_tx_utils is not available
    from solders.transaction import Transaction, VersionedTransaction
    from solders.keypair import Keypair
    USING_RUST_UTILS = False

from .helius_client import HeliusClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('helius_executor')

class HeliusExecutor:
    """
    Executor for sending transactions with Helius RPC.
    """

    def __init__(self, config_path: str = "configs/helius_config.yaml", wallet_keypair_path: str = None):
        """
        Initialize the HeliusExecutor.

        Args:
            config_path: Path to the configuration file
            wallet_keypair_path: Path to the wallet keypair file
        """
        # Load configuration
        self.config = self._load_config(config_path)

        # Extract configuration values
        self.helius_rpc_url = os.getenv("HELIUS_RPC_URL") or self.config['rpc']['primary_url'].replace("${HELIUS_API_KEY}", os.getenv("HELIUS_API_KEY", ""))
        self.fallback_rpc_url = os.getenv("FALLBACK_RPC_URL") or self.config['rpc']['fallback_url']
        self.max_retries = self.config['transaction']['max_retries']
        self.retry_delay = self.config['transaction']['retry_delay']
        self.timeout = self.config['transaction']['timeout']
        self.skip_preflight = self.config['transaction']['skip_preflight']

        # LIVE TRADING MODE: Always use mainnet for real trading
        self.paper_trading = False  # REMOVED: No paper trading in live system
        logger.info("LIVE TRADING MODE: All transactions will be executed on mainnet")

        # Load wallet keypair if provided
        self.keypair = None
        if wallet_keypair_path:
            try:
                with open(wallet_keypair_path, 'r') as f:
                    keypair_bytes = bytes(json.load(f))
                    self.keypair = Keypair.from_bytes(keypair_bytes)
                    logger.info(f"Loaded wallet keypair with public key: {self.keypair.pubkey()}")
            except Exception as e:
                logger.error(f"Failed to load wallet keypair: {str(e)}")
                logger.warning("Continuing without wallet keypair - transactions will not be signed")

        # If no keypair was loaded and we're in paper trading mode, we don't need a real keypair
        # The paper trading mode will simulate transactions without actually signing them
        if self.keypair is None and self.paper_trading:
            logger.info("Paper trading mode active - no real keypair needed for simulation")

        # Circuit breaker settings
        self.circuit_breaker_enabled = self.config['circuit_breaker']['enabled']
        self.failure_threshold = self.config['circuit_breaker']['failure_threshold']
        self.reset_timeout = self.config['circuit_breaker']['reset_timeout']

        # Circuit breaker state
        self.consecutive_failures = 0
        self.circuit_open = False
        self.last_circuit_break = 0

        # Metrics
        self.metrics = {
            'total_transactions': 0,
            'successful_transactions': 0,
            'failed_transactions': 0,
            'paper_trading_transactions': 0,
            'circuit_breaks': 0,
            'avg_execution_time': 0.0,
            'last_execution_time': 0.0,
            'last_updated': time.time()
        }

        # Transaction history
        self.tx_history = []
        self.tx_history_file = Path(self.config['monitoring']['metrics_path'])
        self.tx_history_file.parent.mkdir(exist_ok=True)

        # Initialize Helius client
        self.helius_client = HeliusClient(
            rpc_url=self.helius_rpc_url,
            fallback_rpc_url=self.fallback_rpc_url,
            max_retries=self.max_retries,
            retry_delay=self.retry_delay
        )

        logger.info(f"Initialized Helius executor with RPC URL: {self.helius_rpc_url}")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from YAML file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Dict containing configuration values
        """
        # Default configuration
        default_config = {
            'rpc': {
                'primary_url': 'https://rpc.helius.xyz/?api-key=${HELIUS_API_KEY}',
                'fallback_url': 'https://api.mainnet-beta.solana.com',
                'ws_url': 'wss://rpc.helius.xyz/?api-key=${HELIUS_API_KEY}'
            },
            'api': {
                'base_url': 'https://api.helius.xyz/v0',
                'api_key': '${HELIUS_API_KEY}'
            },
            'transaction': {
                'max_retries': 3,
                'retry_delay': 1.0,
                'timeout': 30.0,
                'skip_preflight': False,
                'max_confirmation_blocks': 32,
                'commitment': 'confirmed'
            },
            'circuit_breaker': {
                'failure_threshold': 5,
                'reset_timeout': 60.0,
                'enabled': True
            },
            'monitoring': {
                'enabled': True,
                'metrics_path': 'output/helius_metrics.json',
                'update_interval': 60.0,
                'verbose_logging': False
            },
            'enhanced_features': {
                'use_das_api': True,
                'use_enhanced_history': True,
                'use_webhooks': False,
                'webhook_url': ''
            }
        }

        try:
            # Load configuration from file if it exists
            if os.path.exists(config_path):
                with open(config_path, 'r') as file:
                    config = yaml.safe_load(file)
                logger.info(f"Loaded configuration from {config_path}")

                # Merge with default config to ensure all keys exist
                merged_config = default_config.copy()
                for section, values in config.items():
                    if section in merged_config and isinstance(values, dict):
                        merged_config[section].update(values)
                    else:
                        merged_config[section] = values

                return merged_config
            else:
                logger.warning(f"Configuration file {config_path} not found, using default configuration")
                return default_config
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            return default_config

    def _sign_transaction(self, transaction: Union[Transaction, VersionedTransaction]) -> bool:
        """
        Sign a transaction with the wallet keypair.

        Args:
            transaction: Transaction to sign

        Returns:
            True if signed successfully, False otherwise
        """
        if self.keypair is None:
            logger.warning("No keypair available for signing")
            return False

        try:
            if USING_RUST_UTILS:
                # Use our PyO3-based package for signing
                # First, serialize the transaction
                tx_bytes = transaction.serialize()

                # Get keypair bytes
                keypair_bytes = self.keypair.to_bytes()

                # Determine if it's a versioned transaction
                is_versioned = isinstance(transaction, VersionedTransaction)

                # Sign the transaction
                signed_bytes = sign_transaction(tx_bytes, keypair_bytes, is_versioned)

                # Deserialize the signed transaction back into the original object
                # This is a bit of a hack, but it works for now
                # In a real implementation, we would replace the transaction with the signed one
                if is_versioned:
                    signed_tx = VersionedTransaction.deserialize(signed_bytes)
                    transaction.signatures = signed_tx.signatures
                else:
                    signed_tx = Transaction.deserialize(signed_bytes)
                    transaction.signatures = signed_tx.signatures
            else:
                # Fallback to solders
                # Handle both legacy and versioned transactions
                if isinstance(transaction, VersionedTransaction):
                    # For versioned transactions, we need to sign the message
                    transaction.sign([self.keypair])
                else:
                    # For legacy transactions
                    transaction.sign_partial([self.keypair])
            return True
        except Exception as e:
            logger.error(f"Failed to sign transaction: {str(e)}")
            return False

    async def execute_transaction(self, tx: Union[bytes, str, Dict[str, Any], Transaction, VersionedTransaction], opts: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a transaction using Helius RPC.

        Args:
            tx: Transaction as bytes, string, Dict (for paper trading), or Transaction/VersionedTransaction object
            opts: Optional transaction options

        Returns:
            Dict containing the execution result
        """
        # Check if circuit breaker is open
        if self.circuit_breaker_enabled and self.circuit_open:
            # Check if it's time to reset the circuit
            if time.time() - self.last_circuit_break > self.reset_timeout:
                logger.info("Resetting circuit breaker")
                self.circuit_open = False
                self.consecutive_failures = 0
            else:
                logger.warning("Circuit breaker is open, rejecting transaction")
                return {
                    'success': False,
                    'error': 'Circuit breaker is open',
                    'circuit_breaker': True
                }

        # Update metrics and record start time
        self.metrics['total_transactions'] += 1
        start_time = time.time()  # Record start time for metrics

        # Sign transaction if it's a Transaction or VersionedTransaction object and we have a keypair
        if isinstance(tx, (Transaction, VersionedTransaction)) and self.keypair:
            if not self._sign_transaction(tx):
                logger.warning("Failed to sign transaction, continuing with unsigned transaction")

        # Prepare transaction options
        tx_opts = {
            'skip_preflight': self.skip_preflight,
            'max_retries': self.max_retries,
            'encoding': 'base58'  # Default to base58 encoding
        }
        if opts:
            tx_opts.update(opts)

        # Execute real transaction only - no simulation modes
        result = await self.helius_client.send_transaction(tx, tx_opts)

        # Update metrics
        execution_time = time.time() - start_time
        self.metrics['last_execution_time'] = execution_time

        if result['success']:
            # Reset consecutive failures
            self.consecutive_failures = 0

            # Update metrics
            self.metrics['successful_transactions'] += 1
            self.metrics['avg_execution_time'] = (
                (self.metrics['avg_execution_time'] * (self.metrics['successful_transactions'] - 1)) +
                execution_time
            ) / self.metrics['successful_transactions']

            # Add to transaction history
            tx_record = {
                'timestamp': datetime.now().isoformat(),
                'signature': result['signature'],
                'provider': result['provider'],
                'execution_time': execution_time,
                'success': True
            }
            self.tx_history.append(tx_record)

            # Save metrics periodically
            if len(self.tx_history) % 10 == 0:
                self._save_metrics()

            logger.info(f"Transaction executed successfully: {result['signature']}")
        else:
            # Update consecutive failures
            self.consecutive_failures += 1

            # Update metrics
            self.metrics['failed_transactions'] += 1

            # Add to transaction history
            tx_record = {
                'timestamp': datetime.now().isoformat(),
                'error': result.get('error', 'Unknown error'),
                'provider': result.get('provider'),
                'execution_time': execution_time,
                'success': False
            }
            self.tx_history.append(tx_record)

            # Check if circuit breaker should trip
            if self.circuit_breaker_enabled and self.consecutive_failures >= self.failure_threshold:
                logger.warning(f"Circuit breaker tripped after {self.consecutive_failures} consecutive failures")
                self.circuit_open = True
                self.last_circuit_break = time.time()
                self.metrics['circuit_breaks'] += 1

            # Save metrics on failure
            self._save_metrics()

            logger.error(f"Transaction execution failed: {result.get('error')}")

        return result

    def _save_metrics(self) -> None:
        """Save metrics and transaction history to disk."""
        try:
            # Update last updated timestamp
            self.metrics['last_updated'] = time.time()

            # Prepare data to save
            data = {
                'metrics': self.metrics,
                'tx_history': self.tx_history[-100:]  # Keep only the last 100 transactions
            }

            # Save to file
            with open(self.tx_history_file, 'w') as f:
                json.dump(data, f, indent=2)

            logger.debug(f"Saved metrics to {self.tx_history_file}")
        except Exception as e:
            logger.error(f"Failed to save metrics: {str(e)}")

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get executor metrics.

        Returns:
            Dict containing metrics data
        """
        # Get client metrics
        client_metrics = self.helius_client.get_metrics()

        # Combine with executor metrics
        combined_metrics = {
            'executor': self.metrics,
            'client': client_metrics
        }

        return combined_metrics

    async def close(self) -> None:
        """Close the executor and its client."""
        await self.helius_client.close()
        self._save_metrics()
        logger.info("Helius executor closed")
