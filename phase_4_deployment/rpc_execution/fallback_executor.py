#!/usr/bin/env python3
"""
Fallback Executor Module

This module provides a fallback executor for transaction execution
when the primary executor fails.
"""

import os
import sys
import json
import logging
import asyncio
import time
import base64
from typing import Dict, List, Any, Optional, Union
from solders.commitment_config import CommitmentLevel
from solders.rpc.config import RpcSendTransactionConfig
from solders.transaction import Transaction
from solders.pubkey import Pubkey as PublicKey
from solders.keypair import Keypair

# Add parent directory to path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import secure wallet
from phase_4_deployment.wallet_sync.secure_wallet import SecureWallet

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'output', 'execution_log.txt'
        )),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fallback_executor')

class FallbackExecutor:
    """
    Fallback executor for transaction execution when the primary executor fails.
    """

    def __init__(self,
                 config_path: str = None,
                 rpc_url: str = None,
                 wallet_address: str = None,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 dry_run: bool = False):
        """
        Initialize the fallback executor.

        Args:
            config_path: Path to the configuration file
            rpc_url: RPC URL (overrides config)
            wallet_address: Public key of the wallet to use (overrides environment)
            max_retries: Maximum number of retry attempts (overrides config)
            retry_delay: Delay between retry attempts in seconds (overrides config)
            dry_run: Whether to run in dry-run mode (no actual transactions)
        """
        # Load configuration
        self.config = self._load_config(config_path)

        # Override config with provided parameters
        self.rpc_url = rpc_url or self.config['rpc']['fallback_url']
        self.wallet_address = wallet_address or os.environ.get('WALLET_ADDRESS')
        self.max_retries = max_retries or self.config['transaction']['max_retries']
        self.retry_delay = retry_delay or self.config['transaction']['retry_delay']
        self.dry_run = dry_run

        # Initialize HTTP client
        try:
            import httpx
            self.http_client = httpx.AsyncClient(timeout=30.0)
        except ImportError:
            logger.error("httpx library not found. Please install it with: pip install httpx")
            raise

        # Initialize secure wallet
        self.secure_wallet = SecureWallet()
        self.keypair = None

        # Load wallet if not in dry run mode
        if not self.dry_run and self.wallet_address:
            try:
                # Load wallet from secure storage
                public_key, private_key = self.secure_wallet.load_wallet(self.wallet_address)
                self.keypair = Keypair.from_base58_string(private_key)
                logger.info(f"Loaded wallet: {public_key}")
            except Exception as e:
                logger.error(f"Failed to load wallet: {str(e)}")
                logger.warning("Running without a wallet - transactions will not be signed")

        # Transaction history
        self.tx_history = []

        # Circuit breaker
        self.failure_count = 0
        self.last_failure_time = 0
        self.circuit_open = False
        self.circuit_breaker_enabled = self.config['circuit_breaker']['enabled']
        self.failure_threshold = self.config['circuit_breaker']['failure_threshold']
        self.reset_timeout = self.config['circuit_breaker']['reset_timeout']

        logger.info(f"Initialized FallbackExecutor with RPC URL: {self.rpc_url}")
        if self.dry_run:
            logger.info("Running in DRY RUN mode - no actual transactions will be executed")

    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """
        Load the configuration from YAML.

        Args:
            config_path: Path to the configuration file

        Returns:
            Dict containing configuration parameters
        """
        # Default config path
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'config.yaml'
            )

        # Default configuration
        default_config = {
            'rpc': {
                'fallback_url': 'https://api.mainnet-beta.solana.com'
            },
            'transaction': {
                'max_retries': 3,
                'retry_delay': 1.0,
                'timeout': 30.0,
                'skip_preflight': False,
                'max_confirmation_blocks': 32
            },
            'circuit_breaker': {
                'failure_threshold': 5,
                'reset_timeout': 60.0,
                'enabled': True
            },
            'monitoring': {
                'enabled': True,
                'metrics_path': 'output/fallback_metrics.json',
                'update_interval': 60.0,
                'verbose_logging': False
            }
        }

        try:
            # Load configuration from file if it exists
            if os.path.exists(config_path):
                import yaml
                with open(config_path, 'r') as file:
                    config = yaml.safe_load(file)
                logger.info(f"Loaded configuration from {config_path}")

                # Extract relevant sections
                if 'solana' in config:
                    default_config['rpc']['fallback_url'] = config['solana'].get('fallback_rpc_url', default_config['rpc']['fallback_url'])

                if 'execution' in config:
                    default_config['transaction']['max_retries'] = config['execution'].get('max_order_retries', default_config['transaction']['max_retries'])
                    default_config['transaction']['skip_preflight'] = config['execution'].get('skip_preflight', default_config['transaction']['skip_preflight'])

                if 'risk' in config:
                    default_config['circuit_breaker']['enabled'] = config['risk'].get('circuit_breaker_enabled', default_config['circuit_breaker']['enabled'])

                return default_config
            else:
                logger.warning(f"Configuration file {config_path} not found, using default configuration")
                return default_config
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            return default_config

    def _sign_transaction(self, transaction: Transaction) -> bool:
        """
        Sign a transaction with the wallet keypair.

        Args:
            transaction: Transaction to sign

        Returns:
            True if signed successfully, False otherwise
        """
        if self.keypair is None:
            logger.warning("No keypair available for signing")
            return False

        try:
            transaction.sign_partial([self.keypair])
            return True
        except Exception as e:
            logger.error(f"Failed to sign transaction: {str(e)}")
            return False

    def _check_circuit_breaker(self) -> bool:
        """
        Check if the circuit breaker is open.

        Returns:
            True if the circuit is closed (can proceed), False if open (should not proceed)
        """
        if not self.circuit_breaker_enabled:
            return True

        # Check if circuit is open
        if self.circuit_open:
            # Check if reset timeout has elapsed
            if time.time() - self.last_failure_time > self.reset_timeout:
                logger.info("Circuit breaker reset timeout elapsed, closing circuit")
                self.circuit_open = False
                self.failure_count = 0
                return True
            else:
                logger.warning("Circuit breaker open, rejecting transaction")
                return False

        return True

    def _update_circuit_breaker(self, success: bool) -> None:
        """
        Update the circuit breaker state.

        Args:
            success: Whether the operation was successful
        """
        if not self.circuit_breaker_enabled:
            return

        if success:
            # Reset failure count on success
            self.failure_count = 0
        else:
            # Increment failure count on failure
            self.failure_count += 1
            self.last_failure_time = time.time()

            # Open circuit if failure threshold reached
            if self.failure_count >= self.failure_threshold:
                logger.warning(f"Failure threshold reached ({self.failure_count} failures), opening circuit breaker")
                self.circuit_open = True

    async def send_transaction(self, transaction: Transaction, opts: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Send a transaction using the fallback RPC.

        Args:
            transaction: Transaction to send
            opts: Transaction options

        Returns:
            Dict containing transaction result with keys:
                - success: Whether the transaction was sent successfully
                - signature: Transaction signature if successful
                - error: Error message if unsuccessful
        """
        # Check circuit breaker
        if not self._check_circuit_breaker():
            return {
                'success': False,
                'error': "Circuit breaker open",
                'provider': 'fallback'
            }

        # Default options
        if opts is None:
            opts = {
                'skip_preflight': self.config['transaction']['skip_preflight'],
                'max_retries': 0  # We handle retries ourselves
            }

        # Add recent blockhash if not already set
        if transaction.recent_blockhash is None:
            try:
                # Use the HTTP client to get a recent blockhash
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getRecentBlockhash",
                    "params": [{"commitment": "confirmed"}]
                }

                response = await self.http_client.post(
                    self.rpc_url,
                    json=payload
                )
                response.raise_for_status()
                result = response.json()

                if 'result' in result and 'value' in result['result'] and 'blockhash' in result['result']['value']:
                    transaction.recent_blockhash = result['result']['value']['blockhash']
                else:
                    logger.error(f"Failed to get recent blockhash: {result.get('error')}")
                    self._update_circuit_breaker(False)
                    return {
                        'success': False,
                        'error': f"Failed to get recent blockhash: {result.get('error')}",
                        'provider': 'fallback'
                    }
            except Exception as e:
                logger.error(f"Failed to get recent blockhash: {str(e)}")
                self._update_circuit_breaker(False)
                return {
                    'success': False,
                    'error': f"Failed to get recent blockhash: {str(e)}",
                    'provider': 'fallback'
                }

        # Sign transaction with wallet keypair
        self._sign_transaction(transaction)

        # Serialize transaction
        try:
            # Handle both legacy and versioned transactions
            if hasattr(transaction, 'message') and hasattr(transaction.message, 'serialize'):
                # This is a versioned transaction with MessageV0
                message_bytes = transaction.message.serialize()
                signatures = [bytes(sig) for sig in transaction.signatures]
                serialized_tx = bytes([len(signatures)]) + b''.join(signatures) + message_bytes
            else:
                # This is a legacy transaction
                serialized_tx = transaction.serialize()

            encoded_tx = base64.b64encode(serialized_tx).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to serialize transaction: {str(e)}")
            self._update_circuit_breaker(False)
            return {
                'success': False,
                'error': f"Failed to serialize transaction: {str(e)}",
                'provider': 'fallback'
            }

        # Execute real transaction only - no simulation modes

        # Try to send transaction with retries
        for attempt in range(self.max_retries):
            try:
                # Prepare RPC request
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "sendTransaction",
                    "params": [
                        encoded_tx,
                        {
                            "skipPreflight": opts.get('skip_preflight', False),
                            "preflightCommitment": "confirmed",
                            "encoding": "base64"
                        }
                    ]
                }

                # Send transaction
                response = await self.http_client.post(
                    self.rpc_url,
                    json=payload
                )
                response.raise_for_status()
                result = response.json()

                if 'result' in result:
                    # Transaction sent successfully
                    signature = result['result']

                    # Record in history
                    self.tx_history.append({
                        'timestamp': time.time(),
                        'signature': signature,
                        'status': 'sent_fallback',
                        'provider': 'fallback'
                    })

                    logger.info(f"Transaction sent successfully via fallback: {signature}")
                    self._update_circuit_breaker(True)

                    return {
                        'success': True,
                        'signature': signature,
                        'provider': 'fallback'
                    }
                else:
                    # Transaction failed
                    error = result.get('error', {}).get('message', 'Unknown error')
                    logger.warning(f"Failed to send transaction (attempt {attempt+1}/{self.max_retries}): {error}")

                    # Some errors are terminal and shouldn't be retried
                    if 'already processed' in error or 'blockhash not found' in error:
                        self._update_circuit_breaker(False)
                        return {
                            'success': False,
                            'error': error,
                            'provider': 'fallback'
                        }
            except Exception as e:
                logger.error(f"Error sending transaction (attempt {attempt+1}/{self.max_retries}): {str(e)}")

            # Wait before retry
            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay)

        # All attempts failed
        logger.error("Failed to send transaction after all attempts")
        self._update_circuit_breaker(False)

        return {
            'success': False,
            'error': "Failed to send transaction after all attempts",
            'provider': 'fallback'
        }

    async def get_transaction_status(self, signature: str) -> Dict[str, Any]:
        """
        Get the status of a transaction.

        Args:
            signature: Transaction signature

        Returns:
            Dict containing transaction status with keys:
                - success: Whether the status was retrieved successfully
                - status: Transaction status information if successful
                - error: Error message if unsuccessful
        """
        # Get real transaction status only - no simulation

        try:
            # Prepare RPC request
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getSignatureStatuses",
                "params": [
                    [signature],
                    {"searchTransactionHistory": True}
                ]
            }

            # Send request
            response = await self.http_client.post(
                self.rpc_url,
                json=payload
            )
            response.raise_for_status()
            result = response.json()

            if 'result' in result and 'value' in result['result']:
                # Extract status
                status = result['result']['value'][0]

                if status is None:
                    return {
                        'success': True,
                        'status': None,
                        'provider': 'fallback',
                        'message': 'Transaction not found'
                    }

                return {
                    'success': True,
                    'status': status,
                    'provider': 'fallback'
                }
            else:
                error = result.get('error', {}).get('message', 'Unknown error')
                logger.error(f"Failed to get transaction status: {error}")

                return {
                    'success': False,
                    'error': error,
                    'provider': 'fallback'
                }
        except Exception as e:
            logger.error(f"Error getting transaction status: {str(e)}")

            return {
                'success': False,
                'error': str(e),
                'provider': 'fallback'
            }

    def save_tx_history(self, file_path: str = None) -> None:
        """
        Save transaction history to file.

        Args:
            file_path: Path to save the transaction history file
        """
        if file_path is None:
            file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'output', 'fallback_tx_history.json'
            )

        try:
            # Load existing history if file exists
            existing_history = []
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        existing_history = data.get('transactions', [])
                except Exception as e:
                    logger.warning(f"Failed to load existing transaction history: {str(e)}")

            # Merge with new history
            merged_history = existing_history + self.tx_history

            # Remove duplicates based on signature
            unique_history = []
            seen_signatures = set()
            for tx in merged_history:
                signature = tx.get('signature')
                if signature and signature not in seen_signatures:
                    unique_history.append(tx)
                    seen_signatures.add(signature)

            # Sort by timestamp (descending)
            sorted_history = sorted(unique_history, key=lambda x: x.get('timestamp', 0), reverse=True)

            # Save to file
            with open(file_path, 'w') as f:
                json.dump({
                    'last_updated': time.time(),
                    'transactions': sorted_history
                }, f, indent=2)

            logger.info(f"Saved {len(sorted_history)} transactions to history")

            # Reset in-memory history after saving
            self.tx_history = []
        except Exception as e:
            logger.error(f"Failed to save transaction history: {str(e)}")

    async def close(self):
        """Close the HTTP client and save history."""
        # Save transaction history
        self.save_tx_history()

        # Close HTTP client
        await self.http_client.aclose()

        logger.info("FallbackExecutor closed")
