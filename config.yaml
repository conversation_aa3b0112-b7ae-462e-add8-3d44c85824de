# Q5 System Configuration
# Central configuration file for runner and backtester

# System mode - LIVE TRADING CONFIGURATION
mode:
  live_trading: true
  paper_trading: false
  backtesting: false
  simulation: false
  test_mode: false
  dry_run: false

# RPC Configuration (Unified) - 🔧 PHASE 2: QUICKNODE RPC AS PRIMARY
rpc:
  primary_url: ${QUICKNODE_RPC_URL}
  fallback_url: ${HELIUS_RPC_URL}
  jito_url: ${JITO_RPC_URL:-https://mainnet.block-engine.jito.wtf/api/v1}
  quicknode_bundle_url: ${QUICKNODE_BUNDLE_URL:-https://api.quicknode.com/v1/solana/mainnet/bundles}
  commitment: confirmed
  max_retries: 3
  timeout: 30

  # 🔧 PHASE 2: RPC Provider Priority
  providers:
    - name: "quicknode_primary"
      url: ${QUICKNODE_RPC_URL}
      priority: 1
      timeout: 15
      max_connections: 20
    - name: "helius_fallback"
      url: ${HELIUS_RPC_URL}
      priority: 2
      timeout: 30
      max_connections: 10

# Solana configuration (Legacy - for backward compatibility)
solana:
  rpc_url: ${HELIUS_RPC_URL}
  private_rpc_url: ${HELIUS_RPC_URL}
  fallback_rpc_url: ${FALLBACK_RPC_URL}
  commitment: confirmed
  max_retries: 3
  retry_delay: 1.0
  tx_timeout: 30
  provider: "helius"  # Using Helius as the primary RPC provider

# DEX Configuration (Unified)
dex:
  jupiter:
    api_url: ${JUPITER_API_URL:-https://quote-api.jup.ag/v6}
    default_slippage_bps: ${JUPITER_SLIPPAGE_BPS:-50}
    timeout_seconds: ${JUPITER_TIMEOUT:-5}
    max_accounts: ${JUPITER_MAX_ACCOUNTS:-20}
    auto_slippage: ${JUPITER_AUTO_SLIPPAGE:-true}

# QuickNode Bundle Configuration - 🔧 PHASE 1 COMPLETE
quicknode_bundles:
  enabled: ${QUICKNODE_BUNDLES_ENABLED:-true}
  api_url: ${QUICKNODE_BUNDLE_URL:-https://api.quicknode.com/v1/solana/mainnet/bundles}
  api_key: ${QUICKNODE_API_KEY}
  max_bundle_size: ${QUICKNODE_MAX_BUNDLE_SIZE:-5}
  bundle_timeout: ${QUICKNODE_BUNDLE_TIMEOUT:-30}
  priority_fee_lamports: ${QUICKNODE_PRIORITY_FEE:-20000}
  retry_attempts: ${QUICKNODE_RETRY_ATTEMPTS:-3}
  fallback_to_jito: ${QUICKNODE_FALLBACK_JITO:-true}

# QuickNode Price Feeds Configuration - 🔧 PHASE 2: COMPLETE
quicknode_price_feeds:
  enabled: ${QUICKNODE_PRICE_FEEDS_ENABLED:-true}
  api_url: ${QUICKNODE_PRICE_API_URL:-https://api.quicknode.com/v1/solana/mainnet/prices}
  api_key: ${QUICKNODE_API_KEY}
  cache_duration_seconds: ${QUICKNODE_PRICE_CACHE:-30}
  timeout_seconds: ${QUICKNODE_PRICE_TIMEOUT:-10}
  retry_attempts: ${QUICKNODE_PRICE_RETRIES:-3}
  fallback_to_jupiter: ${QUICKNODE_PRICE_FALLBACK_JUPITER:-true}
  fallback_to_coingecko: ${QUICKNODE_PRICE_FALLBACK_COINGECKO:-true}

# QuickNode Yellowstone gRPC Streaming - 🔧 PHASE 3: NEW INTEGRATION
quicknode_streaming:
  enabled: ${QUICKNODE_STREAMING_ENABLED:-true}
  grpc_endpoint: ${QUICKNODE_GRPC_ENDPOINT:-grpc.solana.com:443}
  api_key: ${QUICKNODE_API_KEY}

  # Streaming subscriptions
  subscriptions:
    accounts: ${QUICKNODE_STREAM_ACCOUNTS:-true}
    transactions: ${QUICKNODE_STREAM_TRANSACTIONS:-true}
    blocks: ${QUICKNODE_STREAM_BLOCKS:-false}
    slots: ${QUICKNODE_STREAM_SLOTS:-false}

  # Whale detection settings
  whale_detection:
    enabled: ${QUICKNODE_WHALE_DETECTION:-true}
    min_transaction_value_sol: ${QUICKNODE_WHALE_MIN_SOL:-100}
    min_transaction_value_usd: ${QUICKNODE_WHALE_MIN_USD:-15000}
    track_wallets: ${QUICKNODE_WHALE_TRACK_WALLETS:-true}

  # Performance settings
  buffer_size: ${QUICKNODE_STREAM_BUFFER:-1000}
  reconnect_delay: ${QUICKNODE_RECONNECT_DELAY:-5}
  max_reconnect_attempts: ${QUICKNODE_MAX_RECONNECTS:-10}
  heartbeat_interval: ${QUICKNODE_HEARTBEAT:-30}

# Timeouts Configuration (Unified)
timeouts:
  http_client: ${HTTP_CLIENT_TIMEOUT:-30.0}
  jupiter_quote: ${JUPITER_QUOTE_TIMEOUT:-5.0}
  transaction_confirmation: ${TX_CONFIRMATION_TIMEOUT:-30.0}
  bundle_confirmation: ${BUNDLE_CONFIRMATION_TIMEOUT:-30.0}

# Circuit Breaker Configuration (Unified)
circuit_breaker:
  enabled: ${CIRCUIT_BREAKER_ENABLED:-true}
  failure_threshold: ${CIRCUIT_BREAKER_THRESHOLD:-3}
  reset_timeout: ${CIRCUIT_BREAKER_RESET:-60}

# Token Mappings (Unified)
tokens:
  SOL: "So11111111111111111111111111111111111111112"
  USDC: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
  USDT: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"

# Wallet configuration - 🚀 FIXED: Enhanced for larger position sizing
wallet:
  address: ${WALLET_ADDRESS}
  private_key: ${WALLET_PRIVATE_KEY}
  keypair_path: ${KEYPAIR_PATH}
  state_sync_interval: 60  # seconds
  position_update_interval: 300  # seconds
  max_positions: 10
  data_dir: "phase_0_env_setup/data"
  active_trading_pct: 0.9  # 🚀 FIXED: Use 90% of wallet instead of 50%
  reserve_pct: 0.1         # 🚀 FIXED: Only keep 10% in reserve instead of 50%
  min_balance_warning: 1.0
  min_trading_balance: 0.5

# Trading configuration - 🚀 FIXED: Enhanced position sizing for fee-viable trades
trading:
  enabled: true
  base_position_size_pct: 0.20  # 🚀 FIXED: Quadrupled from 0.05 to make trades viable against fees
  max_position_size_pct: 0.40   # 🚀 FIXED: Quadrupled from 0.10 for meaningful trade sizes
  min_position_size_pct: 0.05   # 🚀 FIXED: Increased minimum for fee-viable trades
  max_trades_per_day: 24        # 🎯 WINNING: Max 1 trade per hour (reduce fees)
  max_trades_per_hour: 6        # 🎯 WINNING: Max 6 trades per hour
  min_trade_interval: 300       # 🎯 WINNING: 5 minutes between trades (reduce fees)
  min_trade_size_usd: 50        # 🎯 WINNING: Increased for better fee efficiency
  target_trade_size_usd: 200    # 🎯 WINNING: Larger trades for economies of scale
  update_interval: 10           # 🔧 REMOVED FILTER: Faster updates

# Enhanced Market Regime Detection
market_regime:
  enabled: ${MARKET_REGIME_ENABLED:-true}
  adaptive_thresholds: ${ADAPTIVE_THRESHOLDS:-true}
  volatility_lookback_periods: [20, 50, 100]
  adx_period: ${ADX_PERIOD:-14}
  adx_threshold_base: ${ADX_THRESHOLD_BASE:-25}
  adx_threshold_multiplier: ${ADX_THRESHOLD_MULTIPLIER:-1.2}
  bb_period: ${BB_PERIOD:-20}
  bb_std_dev: ${BB_STD_DEV:-2}
  atr_period: ${ATR_PERIOD:-14}
  volatility_threshold: ${VOLATILITY_THRESHOLD:-0.03}
  range_period: ${RANGE_PERIOD:-20}
  range_threshold: ${RANGE_THRESHOLD:-0.05}
  choppiness_period: ${CHOPPINESS_PERIOD:-14}
  choppiness_threshold_base: ${CHOPPINESS_THRESHOLD_BASE:-61.8}
  choppiness_threshold_multiplier: ${CHOPPINESS_THRESHOLD_MULTIPLIER:-1.1}
  regime_confidence_threshold: ${REGIME_CONFIDENCE_THRESHOLD:-0.7}
  regime_change_cooldown: ${REGIME_CHANGE_COOLDOWN:-300}
  regime_change_lookback: ${REGIME_CHANGE_LOOKBACK:-5}
  ml_models:
    hmm_enabled: ${HMM_ENABLED:-true}
    hmm_states: ${HMM_STATES:-4}
    hmm_lookback_days: ${HMM_LOOKBACK_DAYS:-30}
    retrain_interval_hours: ${RETRAIN_INTERVAL_HOURS:-24}

# Whale Watching Integration
whale_watching:
  enabled: ${WHALE_WATCHING_ENABLED:-true}
  min_transaction_threshold_usd: ${WHALE_MIN_TRANSACTION:-100000}
  whale_confidence_weight: ${WHALE_CONFIDENCE_WEIGHT:-0.3}
  whale_signal_decay_hours: ${WHALE_SIGNAL_DECAY:-6}
  whale_discovery_interval: ${WHALE_DISCOVERY_INTERVAL:-3600}
  whale_activity_lookback_hours: ${WHALE_LOOKBACK_HOURS:-24}
  whale_signal_filters:
    min_whale_count: ${WHALE_MIN_COUNT:-3}
    min_transaction_volume: ${WHALE_MIN_VOLUME:-500000}

# Advanced Risk Management
risk_management:
  var_enabled: ${VAR_ENABLED:-true}
  var_confidence_levels: [0.95, 0.99]
  var_lookback_days: ${VAR_LOOKBACK_DAYS:-252}
  cvar_enabled: ${CVAR_ENABLED:-true}
  portfolio_var_limit_pct: ${PORTFOLIO_VAR_LIMIT:-0.02}
  correlation_threshold: ${CORRELATION_THRESHOLD:-0.7}
  max_correlated_exposure_pct: ${MAX_CORRELATED_EXPOSURE:-0.3}
  max_position_size_pct: ${MAX_POSITION_SIZE_PCT:-0.1}
  max_sector_exposure_pct: ${MAX_SECTOR_EXPOSURE_PCT:-0.4}
  max_single_asset_pct: ${MAX_SINGLE_ASSET_PCT:-0.15}
  position_sizing_method: ${POSITION_SIZING_METHOD:-var_based}
  var_target_pct: ${VAR_TARGET_PCT:-0.01}
  var_confidence_level: ${VAR_CONFIDENCE_LEVEL:-0.95}
  regime_based_sizing: ${REGIME_BASED_SIZING:-true}
  correlation_adjustment: ${CORRELATION_ADJUSTMENT:-true}
  correlation_penalty: ${CORRELATION_PENALTY:-0.5}
  volatility_scaling: ${VOLATILITY_SCALING:-true}
  volatility_lookback: ${VOLATILITY_LOOKBACK:-20}
  min_position_size: ${MIN_POSITION_SIZE:-0.01}
  position_size_increment: ${POSITION_SIZE_INCREMENT:-0.01}
  risk_update_interval: ${RISK_UPDATE_INTERVAL:-300}
  correlation_lookback_days: ${CORRELATION_LOOKBACK_DAYS:-60}

# Strategy Performance Attribution
strategy_attribution:
  enabled: ${STRATEGY_ATTRIBUTION_ENABLED:-true}
  attribution_window_days: ${ATTRIBUTION_WINDOW_DAYS:-30}
  min_trades_for_attribution: ${MIN_TRADES_ATTRIBUTION:-10}
  performance_decay_factor: ${PERFORMANCE_DECAY_FACTOR:-0.95}
  rebalance_threshold: ${REBALANCE_THRESHOLD:-0.1}
  max_history_days: ${MAX_HISTORY_DAYS:-90}
  update_interval_minutes: ${UPDATE_INTERVAL_MINUTES:-60}
  benchmark_return: ${BENCHMARK_RETURN:-0.0}
  risk_free_rate: ${RISK_FREE_RATE:-0.02}

# Adaptive Strategy Weighting
adaptive_weighting:
  enabled: ${ADAPTIVE_WEIGHTING_ENABLED:-true}
  learning_rate: ${ADAPTIVE_LEARNING_RATE:-0.01}
  weight_update_interval: ${WEIGHT_UPDATE_INTERVAL:-3600}
  min_strategy_weight: ${MIN_STRATEGY_WEIGHT:-0.1}
  max_strategy_weight: ${MAX_STRATEGY_WEIGHT:-0.6}
  performance_lookback_days: ${PERFORMANCE_LOOKBACK_DAYS:-14}
  momentum_factor: ${MOMENTUM_FACTOR:-0.3}
  mean_reversion_factor: ${MEAN_REVERSION_FACTOR:-0.2}
  risk_adjustment_factor: ${RISK_ADJUSTMENT_FACTOR:-0.5}
  regime_adjustment_factor: ${REGIME_ADJUSTMENT_FACTOR:-0.3}
  performance_threshold_high: ${PERFORMANCE_THRESHOLD_HIGH:-0.02}
  performance_threshold_low: ${PERFORMANCE_THRESHOLD_LOW:--0.01}
  sharpe_threshold: ${SHARPE_THRESHOLD:-1.0}
  drawdown_threshold: ${DRAWDOWN_THRESHOLD:--0.1}
  confidence_threshold: ${CONFIDENCE_THRESHOLD:-0.8}  # 🎯 OPTIMIZED: Increased from 0.6 to 0.8 for profitability
  regime_confidence_weight: ${REGIME_CONFIDENCE_WEIGHT:-0.3}
  performance_weight: ${PERFORMANCE_WEIGHT:-0.4}
  risk_weight: ${RISK_WEIGHT:-0.3}

# Strategy configuration
strategies:
  - name: momentum_sol_usdc
    enabled: true
    params:
      window_size: 5         # 🎯 OPTIMIZED: Proven winning parameter (was 20)
      threshold: 0.005       # 🎯 OPTIMIZED: Proven winning parameter (was 0.01)
      smoothing_factor: 0.1  # 🎯 OPTIMIZED: Proven winning parameter
      max_value: 0.05        # 🎯 OPTIMIZED: Proven winning parameter
      use_filters: true
  - name: opportunistic_volatility_breakout
    enabled: true
    params:
      # 🎯 WINNING: High-confidence profitable strategy parameters
      min_confidence: 0.9    # 🎯 WINNING: Increased to 0.9 for higher quality signals
      risk_level: medium
      volatility_threshold: 0.02
      breakout_threshold: 0.015
      use_filters: true
  - name: wallet_momentum
    enabled: true
    params:
      lookback_period: 24  # hours
      min_wallet_count: 5
      momentum_threshold: 0.1
  - name: alpha_signal_blend
    enabled: false
    params:
      alpha_weight: 0.7
      momentum_weight: 0.3

# Risk management - 🔧 REMOVED FILTERS FOR FULL LIVE TRADING
risk:
  max_position_size_usd: 100000  # 🔧 REMOVED FILTER: Increased to $100k max position
  max_position_size_pct: 1.0     # 🔧 REMOVED FILTER: Allow 100% of portfolio
  stop_loss_pct: 0.15            # 🔧 REMOVED FILTER: Increased stop loss tolerance
  take_profit_pct: 0.008         # 🎯 WINNING: 0.8% minimum profit target (beat fees)
  min_profit_target_pct: 0.005   # 🎯 WINNING: 0.5% minimum profit to execute trade
  max_drawdown_pct: 0.25         # 🔧 REMOVED FILTER: Increased drawdown tolerance
  daily_loss_limit_usd: 20000    # 🔧 REMOVED FILTER: Increased daily loss limit
  circuit_breaker_enabled: false # 🔧 REMOVED FILTER: Disabled circuit breaker

# Risk management settings for position sizer - 🔧 ENHANCED FOR SCALING
risk_management:
  max_portfolio_exposure: 0.8    # 🔧 INCREASED: Allow 80% portfolio exposure (was 0.5)
  max_risk_per_trade: 0.005      # 🎯 OPTIMIZED: 0.5% risk per trade from 0.7826 config
  position_sizer:
    max_position_size: 0.3       # 🔧 INCREASED: Allow 30% max position (was 0.1)
    min_position_size: 0.01      # Keep minimum reasonable
    volatility_scaling: true
    volatility_lookback: 20

# Execution parameters - 🔧 REMOVED FILTERS FOR FULL LIVE TRADING
execution:
  slippage_tolerance: ${SLIPPAGE_TOLERANCE:-0.05}  # 🔧 REMOVED FILTER: Increased to 5% slippage tolerance
  max_spread_pct: ${MAX_SPREAD_PCT:-0.10}          # 🔧 REMOVED FILTER: Increased to 10% spread tolerance
  min_liquidity_usd: ${MIN_LIQUIDITY_USD:-0}       # 🔧 REMOVED FILTER: No minimum liquidity requirement
  order_type: ${ORDER_TYPE:-market}                # market, limit
  retry_failed_orders: ${RETRY_FAILED_ORDERS:-true}
  max_order_retries: ${MAX_ORDER_RETRIES:-10}      # 🔧 REMOVED FILTER: Increased retries
  use_jito: ${USE_JITO:-true}
  priority_fee_lamports: ${PRIORITY_FEE_LAMPORTS:-10000}  # 🔧 REMOVED FILTER: Higher priority fees
  fallback_enabled: ${FALLBACK_ENABLED:-true}
  retry_delay: ${RETRY_DELAY:-0.5}                 # 🔧 REMOVED FILTER: Faster retries
  circuit_breaker_enabled: ${CIRCUIT_BREAKER_ENABLED:-false}  # 🔧 REMOVED FILTER: Disabled circuit breaker

# Filter configuration - 🔧 REMOVED FILTERS FOR FULL LIVE TRADING
filters:
  enabled: false  # 🔧 REMOVED FILTER: Disabled all filters
  alpha_wallet:
    enabled: false  # 🔧 REMOVED FILTER: Disabled alpha wallet filter
    min_wallet_count: 1  # 🔧 REMOVED FILTER: Minimal requirement
    lookback_period: 1   # 🔧 REMOVED FILTER: Minimal lookback
    momentum_threshold: 0.01  # 🔧 REMOVED FILTER: Minimal threshold
  liquidity_guard:
    enabled: false  # 🔧 REMOVED FILTER: Disabled liquidity guard
    min_liquidity_usd: 0  # 🔧 REMOVED FILTER: No minimum liquidity
    order_book_depth: 1   # 🔧 REMOVED FILTER: Minimal depth
  volatility_screener:
    enabled: false  # 🔧 REMOVED FILTER: Disabled volatility screener
    max_volatility: 1.0   # 🔧 REMOVED FILTER: Allow any volatility
    wick_threshold: 1.0   # 🔧 REMOVED FILTER: Allow any wick size

# RL integration
rl_agent:
  enabled: false  # Initially disabled, enable after testing
  data_collection: true
  collection_path: "phase_4_deployment/output/rl_data"

# Backtest configuration
backtest:
  start_date: "2023-01-01"
  end_date: "2023-12-31"
  initial_capital: 10000
  fee_pct: 0.001  # 0.1%
  data_source: "phase_0_env_setup/data/historical"
  output_dir: "phase_2_backtest_engine/output"

# Monitoring and alerts
monitoring:
  enabled: true
  update_interval: 300  # seconds
  telegram_alerts: true
  email_alerts: false
  performance_report_interval: 86400  # daily in seconds
  log_level: "INFO"

# API integrations
apis:
  helius:
    enabled: true
    api_key: ${HELIUS_API_KEY}
    endpoint: "https://api.helius.dev/v0"
    rpc_endpoint: "https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    ws_endpoint: "wss://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}"
    use_enhanced_apis: true
  birdeye:
    enabled: false  # 🔧 DISABLED: Using QuickNode price feeds instead
    api_key: ${BIRDEYE_API_KEY}
    endpoint: "https://public-api.birdeye.so"
    # 🔧 FIXED: Enhanced rate limiting and error handling
    timeout: 10
    max_retries: 5  # Increased for rate limiting
    retry_delay: 2.0  # Longer delay for rate limiting
    rate_limit_delay: 0.5  # Minimum 500ms between calls
    max_requests_per_minute: 100  # Conservative rate limiting
  coingecko:
    enabled: true
    api_key: ${COINGECKO_API_KEY}
    endpoint: "https://api.coingecko.com/api/v3"
  # Pump.fun integration removed

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: true
  log_dir: "logs"
  max_file_size: "10MB"
  backup_count: 5

# Deployment
deployment:
  docker:
    image: "q5system:latest"
    container_name: "q5_trading_bot"
    restart_policy: "unless-stopped"
  streamlit:
    port: 8501
    headless: false
  quantconnect:
    project_id: "your_project_id"
    backtest_id: "your_backtest_id"
